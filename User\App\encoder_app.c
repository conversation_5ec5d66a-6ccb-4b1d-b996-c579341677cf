#include "encoder_app.h"

extern UART_HandleTypeDef huart1;

// 左右编码器电机
Encoder left_encoder;
Encoder right_encoder;

/**
 * @brief 初始化编码器应用
 */
void Encoder_Init(void)
{
  Encoder_Driver_Init(&left_encoder, &htim3, 0);
  Encoder_Driver_Init(&right_encoder, &htim4, 0);
}

/**
 * @brief 编码器应用运行任务 (应由调度器周期性调用)
 */
void Encoder_Task(void)
{
  Encoder_Driver_Update(&left_encoder);
  Encoder_Driver_Update(&right_encoder);
	
//	my_printf(&huart1,"{left_filtered}%.2f\r\n",left_encoder.speed_cm_s);
//	my_printf(&huart1,"{right_filtered}%.2f\r\n",right_encoder.speed_cm_s);
//	my_printf(&huart1,"%.2f,%.2f\r\n",left_encoder.speed_cm_s,right_encoder.speed_cm_s);
//	my_printf(&huart1,"Left:%.2f Right:%.2f\r\n",left_encoder.speed_cm_s,right_encoder.speed_cm_s);
//	my_printf(&huart1,"Left:%d Right:%d\r\n",left_encoder.total_count,right_encoder.total_count);
}
