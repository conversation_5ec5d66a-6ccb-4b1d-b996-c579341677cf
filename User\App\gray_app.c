#include "gray_app.h"
#include "software_iic.h" // 使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error;
float g_line_position_filtered; // 滤波后的位置误差

void Gray_Init(void)
{
    // 测试软件I2C连接
    my_printf(&huart1, "Initializing Gray Sensor with Software I2C (PC4/PC5)...\r\n");

    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully via Software I2C!\r\n");
    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed via Software I2C!\r\n");
    }
}

void Gray_Task(void)
{
//		HAL_NVIC_DisableIRQ(TIM2_IRQn);
		static uint8_t error_count = 0; // 错误计数器
		uint8_t temp = 0;
		temp = IIC_Get_Digtal();
		if(temp == 0xAA)
		{
			error_count++;
			if(error_count >= 10) { // 连续10次错误后报警
//				my_printf(&huart1, "Gray Sensor Communication Error!\r\n");
				error_count = 0; // 重置计数器
			}
			return;
		}
		error_count = 0; // 通信成功，重置错误计数
    Digtal=~temp;
	
//		HAL_NVIC_EnableIRQ(TIM2_IRQn); 
		
//    my_printf(&huart1, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }
    
    if(black_line_count > 0) {
        g_line_position_error = weighted_sum / (float)black_line_count;
        // 简单的一阶低通滤波，滤波系数0.7
        g_line_position_filtered = 0.7f * g_line_position_filtered + 0.3f * g_line_position_error;
    } else {
        // 无线检测时保持上次误差值，避免突变
        // g_line_position_error 保持不变
    }
}
