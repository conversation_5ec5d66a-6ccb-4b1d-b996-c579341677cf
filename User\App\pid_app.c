#include "pid_app.h"

#define V_L_MAX 120
#define V_R_MAX 120

extern UART_HandleTypeDef huart1;

extern Encoder left_encoder;
extern Encoder right_encoder;
extern float right_yaw;
int basic_speed = 40; // 基础速度

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环

PID_T pid_line;        // 循迹环

PID_T pid_angle;       // 角度环

/* PID 参数定义 */
//PidParams_t pid_params_left = {
//    .kp = 6.8f,     // 更强的比例控制，加快爬升速度
//    .ki = 0.175f,     // 提高积分速度，加快靠近目标
//    .kd = 0.5f,     // 适当微分抑制末期过冲
//    .out_min = -V_L_MAX,
//    .out_max = V_L_MAX,
//};

//PidParams_t pid_params_right = {
//    .kp = 7.0f,     // 更强的比例控制，加快爬升速度
//    .ki = 0.2f,     // 提高积分速度，加快靠近目标
//    .kd = 0.4f,     // 适当微分抑制末期过冲
//    .out_min = -V_R_MAX,
//    .out_max = V_R_MAX,
//};
PidParams_t pid_params_left = {
    .kp = 7.5f,     // 增加一点比例响应，提升启动速度
    .ki = 0.22f,    // 补足积分，减少高目标下稳态误差
    .kd = 0.7f,     // 增强微分，减少中高速度下的小幅震荡
		.out_min = -V_L_MAX,
    .out_max = V_L_MAX,
	
};
PidParams_t pid_params_right = {
    .kp = 6.5f,     // 略微减弱比例，降低高速度区的小过冲
    .ki = 0.18f,    // 与左轮保持相近，利于一致性
    .kd = 0.6f,     // 稍加强微分，减少上升段轻微超调
	  .out_min = -V_R_MAX,
    .out_max = V_R_MAX,
};


PidParams_t pid_params_line = {
    .kp = 5.25f,
    .ki = 0.0f,
    .kd = 0.0f,
    .out_min = -80.0f,
    .out_max = 80.0f,
};


PidParams_t pid_params_angle = {
    .kp = 0.3f,        
    .ki = 0.0001f,      
    .kd = 4.50f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);
  

  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
//  
  pid_init(&pid_angle,
           pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
           0.0f, pid_params_angle.out_max);
  
  
  pid_set_target(&pid_speed_left, basic_speed);
  pid_set_target(&pid_speed_right, basic_speed);
  pid_set_target(&pid_line, 0);
  pid_set_target(&pid_angle,140);
}

bool pid_running = false; // PID 控制使能开关

unsigned char pid_control_mode = 0; // 0-角度环控制，1-循迹环控制

void Line_PID_control(void) // 循迹环控制
{
  int line_pid_output = 0;
  
  // 使用位置式 PID 计算利用循迹环计算输出
  line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);
  
  // 输出限幅
  line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
  
  // 将差值作用在速度环的目标量上
//	pid_set_target(&pid_speed_left, basic_speed + line_pid_output);
//	pid_set_target(&pid_speed_right, basic_speed - line_pid_output);
//	pid_set_target(&pid_speed_left, basic_speed);
//  pid_set_target(&pid_speed_right, basic_speed);
		pid_speed_left.target= basic_speed + line_pid_output;
  	pid_speed_right.target = basic_speed - line_pid_output;
}

void Angle_PID_control(void) // 角度环控制
{
  int angle_pid_output = 0;
  
	float Yaw = right_yaw;
	
  // 使用位置式 PID 计算利用角度环计算输出
  angle_pid_output = pid_calculate_positional(&pid_angle, Yaw);
  
  // 输出限幅
  angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);
  
  // 将差值作用在速度环的目标量上
//  pid_set_target(&pid_speed_left, basic_speed - angle_pid_output);
//  pid_set_target(&pid_speed_right, basic_speed + angle_pid_output);
	
	pid_speed_left.target= basic_speed + angle_pid_output;
  pid_speed_right.target = basic_speed - angle_pid_output;
//	my_printf(&huart1, "%f,%f\r\n",right_yaw,angle_pid_output);
}

uint8_t stop_flat = 0;
uint8_t angle_flat = 0;
void PID_Task(void)
{
  if(pid_running == false) return;
  
    float output_left = 0, output_right = 0;
		
//		if(angle_flat == 0 && ((system_mode == 3)||(system_mode == 4)))
//		{
//			pid_set_target(&pid_angle, 35);
//			angle_flat = 1;
//		}
//	
//    if(pid_control_mode == 0) // 角度环控制
//    {
////      if(distance > 20000) // 超过指定距离时减速
////        basic_speed = 75;
////      else
////        basic_speed = 130;
//			basic_speed = 30;
//      
//      Angle_PID_control();
//    }
//    else // 循迹环控制
//    {
//      basic_speed = 30;
//      Line_PID_control();
//    }
	
//		static uint16_t num = 0;
//		static uint8_t temp = 0;
//		if(++temp >= 50)
//		{
//			temp = 0;
//			num += 20;
//		}
//		
//		pid_set_target(&pid_speed_left, num);
//    pid_set_target(&pid_speed_right, num);

//		Line_PID_control();
		Angle_PID_control();
		if(stop_flat == 1)
		{
			 pid_set_target(&pid_speed_left, 0);
			 pid_set_target(&pid_speed_right, 0);
		}
		
    // 使用位置式 PID 计算利用速度环计算输出
    output_left = pid_calculate_positional(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_positional(&pid_speed_right, right_encoder.speed_cm_s);
  
    // 输出限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    
    // 设置电机速度
		float duty_l = output_left  / V_L_MAX * 100.0f;
		float duty_r = output_right / V_R_MAX * 100.0f;
		
		motor_set_l(duty_l);
    motor_set_r(duty_r);
		
//		my_printf(&huart1, "%f,%f\r\n", pid_line.target, g_line_position_error);
//		my_printf(&huart1, "%f,%f,%f\r\n", pid_speed_left.target,right_encoder.speed_cm_s, left_encoder.speed_cm_s);
//		my_printf(&huart1, "%f,%f\r\n", pid_speed_right.target, right_encoder.speed_cm_s);
//		my_printf(&huart1, "%f,%f\r\n", output_left, output_right);
//		my_printf(&huart1, "%f,%f\r\n",pid_angle.target,pid_angle.out);
}


