#include "usart_app.h"

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern UART_HandleTypeDef huart5;
extern DMA_HandleTypeDef hdma_uart5_rx;

HWT101_t hwt101;
float right_yaw;



uint8_t uart_rx_dma_buffer[128]; // DMA 读取缓冲区
uint8_t ring_buffer_input[128]; // 环形缓冲区对应的线性数组
struct rt_ringbuffer ring_buffer; // 环形缓冲区
uint8_t uart_data_buffer[128]; // 数据处理缓冲区


/*hwt101*/
uint8_t hwt101_rx_dma_buffer[128]; // DMA 读取缓冲区
uint8_t hwt101_buffer_input[128]; // 环形缓冲区对应的线性数组
struct rt_ringbuffer hwt101_buffer; // 环形缓冲区
uint8_t hwt101_data_buffer[128]; // 数据处理缓冲区


int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// 初始化可变参数列表
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void uart_init(void)
{

	rt_ringbuffer_init(&ring_buffer, ring_buffer_input, 128);
  
	HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, 128); // 启动读取中断
	__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // 关闭 DMA 的"半满中断"功能

  
}

void hwt101_init(void)
{
	HWT101_Create(&hwt101,&huart5,1000);
	my_printf(&huart1,"Waiting...\r\n");
	  HWT101_StartManualCalibration(&hwt101);
	HAL_Delay(5000);
	HWT101_StopManualCalibration(&hwt101);
	my_printf(&huart1,"OK\r\n");
	HWT101_ResetYaw(&hwt101);
	my_printf(&huart1,"YAW is OK\r\n");
  

	rt_ringbuffer_init(&hwt101_buffer, hwt101_buffer_input, 128);
	
	HAL_UARTEx_ReceiveToIdle_DMA(&huart5, hwt101_rx_dma_buffer, 128); // 启动读取中断
	__HAL_DMA_DISABLE_IT(&hdma_uart5_rx, DMA_IT_HT); // 关闭 DMA 的"半满中断"功能
}




	

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	// 1. 确认是目标串口 (USART1)
	if (huart->Instance == USART1)
	{
		// 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
		//    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&ring_buffer, uart_rx_dma_buffer, Size);

		// 5. 清空 DMA 接收缓冲区，为下次接收做准备
		//    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

		// 6. **关键：重新启动下一次 DMA 空闲接收**
		//    必须再次调用，否则只会接收这一次
		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

		// 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
	}
    if (huart->Instance == UART5)
    {
        // 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
        //    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
        HAL_UART_DMAStop(huart);

        // 3. 将 DMA 缓冲区中有效的数据 (Size 个字节) 复制到待处理缓冲区
        rt_ringbuffer_put(&hwt101_buffer, hwt101_rx_dma_buffer, Size);
        // 注意：这里使用了 Size，只复制实际接收到的数据
        
        // 4. 举起"到货通知旗"，告诉主循环有数据待处理

        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
        //    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
        memset(hwt101_rx_dma_buffer, 0, sizeof(hwt101_rx_dma_buffer));

        // 6. **关键：重新启动下一次 DMA 空闲接收**
        //    必须再次调用，否则只会接收这一次
        HAL_UARTEx_ReceiveToIdle_DMA(&huart5, hwt101_rx_dma_buffer, sizeof(hwt101_rx_dma_buffer));
        
        // 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
         __HAL_DMA_DISABLE_IT(&hdma_uart5_rx, DMA_IT_HT);
    }


}


void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&ring_buffer);

//	my_printf(&huart1, "666\r\n");
	
	if (length == 0)
		return;

	rt_ringbuffer_get(&ring_buffer, uart_data_buffer, length);
	
	uint8_t num = 2;
	int8_t pwm = 2;
	sscanf(uart_data_buffer, "set_%d:%d", &num, &pwm);
	if(num == 0)
	{
//		motor_set_l(pwm);
		pid_set_target(&pid_speed_left, pwm);
		my_printf(&huart1, "set_l_ok:%d\r\n", pwm);
	}
	else if(num == 1)
	{
		motor_set_r(pwm);
		pid_set_target(&pid_speed_right, pwm);
		my_printf(&huart1, "set_r_ok:%d\r\n", pwm);
	}
	else
	{
		my_printf(&huart1, "uart1:%s\r\n", uart_data_buffer);
		my_printf(&huart5, "uart1:%s\r\n", uart_data_buffer);
	}
		
	
	// 清空接收缓冲区
	memset(uart_data_buffer, 0, sizeof(uart_data_buffer));
}

float angle_extend_range(float raw_angle) {
    static float prev_angle = 0.0f;      // 上次角度值
    static int16_t jump_count = 0;       // 跳变累积次数
    static uint8_t first_call = 1;       // 首次调用标志

    if (first_call) { prev_angle = raw_angle; first_call = 0; return raw_angle; }
    if (prev_angle > 150.0f && raw_angle < -150.0f) jump_count++;
    else if (prev_angle < -150.0f && raw_angle > 150.0f) jump_count--;

    prev_angle = raw_angle;
    float extended = raw_angle + jump_count * 360.0f;
    return (extended > 360.0f) ? 360.0f : (extended < -360.0f) ? -360.0f : extended;
}


// 角度范围扩展函数：将-180°~+180°扩展到-360°~+360°，解决180°跳变问题
void hwt101_task(void)
{
  uint16_t uart_data_len = rt_ringbuffer_data_len(&hwt101_buffer);
  if(uart_data_len > 0)
  {
    rt_ringbuffer_get(&hwt101_buffer, hwt101_data_buffer, uart_data_len);
    hwt101_data_buffer[uart_data_len] = '\0';
    /* 数据解析 */
    HWT101_ProcessBuffer(&hwt101, hwt101_data_buffer,uart_data_len);
    float yaw = HWT101_GetYaw(&hwt101);
    float gyro_z = HWT101_GetGyroZ(&hwt101);
    HWT101_Data_t* data = HWT101_GetData(&hwt101);
    if(data != NULL)
    {
		right_yaw = angle_extend_range(yaw); // 使用扩展角度范围，解决180°跳变问题
//		my_printf(&huart1, "Yaw:%.2f, Gyro_z:%.2f\r\n", data->yaw, data->gyro_z);
		my_printf(&huart1, "right_Yaw:%.2f\r\n", right_yaw);
    }
    //my_printf(&huart1, "hwt101:%s\r\n", hwt101_data_buffer);
	
    memset(hwt101_data_buffer, 0, uart_data_len);
  }
}


