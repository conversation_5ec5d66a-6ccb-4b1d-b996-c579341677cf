#ifndef __HARDWARE_IIC_H
#define __HARDWARE_IIC_H

#include "stm32f4xx_hal.h"
#include "main.h"
#include "i2c.h"
#include "gw_grayscale_sensor.h"

/* 硬件I2C句柄定义 */
extern I2C_HandleTypeDef hi2c2;

/* 应用层接口函数声明 - 与software_iic.h保持一致 */
unsigned char IIC_ReadByte(unsigned char Salve_Address);    // 读取单字节
unsigned char IIC_ReadBytes(unsigned char Salve_Address, unsigned char Reg_Address, unsigned char *Result, unsigned char len); // 读取多字节
unsigned char IIC_WriteByte(unsigned char Salve_Address, unsigned char Reg_Address, unsigned char data); // 写入单字节
unsigned char IIC_WriteBytes(unsigned char Salve_Address, unsigned char Reg_Address, unsigned char *data, unsigned char len); // 写入多字节

/* 灰度传感器专用函数声明 */
unsigned char Ping(void);                               // 传感器连接检测
unsigned char IIC_Get_Digtal(void);                     // 获取数字量数据
unsigned char IIC_Get_Anolog(unsigned char *Result, unsigned char len); // 获取模拟量数据
unsigned char IIC_Get_Single_Anolog(unsigned char Channel);  // 获取单通道模拟量
unsigned char IIC_Anolog_Normalize(uint8_t Normalize_channel); // 模拟量归一化
unsigned short IIC_Get_Offset(void);                    // 获取偏移量

#endif

