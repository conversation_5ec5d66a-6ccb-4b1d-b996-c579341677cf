#include "my_scheduler.h"
extern UART_HandleTypeDef huart1;
typedef struct{
	void(*task_fun)(void);
	uint32_t task_time;
	uint32_t last_time;
}task;

task all_task[]={
	{led_task,1,0},
//	{Gray_Task,5,0},
//	{Encoder_Task,5,0},  // 编码器任务，5ms周期
//	{PID_Task,5,0},      // PID控制任务，5ms周期
//	{bno080_task,10,0},  // BNO传感器任务已注释
//	{hwt101_Task,5,0},   // HWT101传感器任务，5ms周期
	{uart_task,5,0},
	{key_task,10,0},
	{oled_task,100,0},
	
};

uint8_t task_num;

void all_task_init(void)
{
	task_num = sizeof(all_task)/sizeof(task);

	my_oled_init();
	uart_init();
//	my_bno080_init();  // BNO传感器初始化已注释
	hwt101_init();     // HWT101传感器初始化

	led_init();
//	PID_Init();
	Encoder_Init();
	Motor_Init();
//	jy901s_init();     // JY901传感器初始化已注释
	Gray_Init();
	timer_init();

//	motor_set_l(30);
//	motor_set_r(30);
}

void all_task_run(void)
{
	for(uint8_t i=0;i<task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= all_task[i].last_time + all_task[i].task_time)
		{
			all_task[i].last_time = now_time;
			all_task[i].task_fun();
		}
	}	
}
