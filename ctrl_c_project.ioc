#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART1_RX
Dma.Request1=UART5_RX
Dma.RequestsNb=2
Dma.UART5_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.1.Instance=DMA1_Stream0
Dma.UART5_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.1.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.1.Mode=DMA_NORMAL
Dma.UART5_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.1.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Standard
I2C1.IPParameters=I2C_Mode
I2C2.ClockSpeed=400000
I2C2.I2C_Mode=I2C_Fast
I2C2.IPParameters=I2C_Mode,ClockSpeed
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP10=UART5
Mcu.IP11=USART1
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM2
Mcu.IP8=TIM3
Mcu.IP9=TIM4
Mcu.IPNb=12
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PC4
Mcu.Pin11=PC5
Mcu.Pin12=PE9
Mcu.Pin13=PE11
Mcu.Pin14=PE13
Mcu.Pin15=PE14
Mcu.Pin16=PB10
Mcu.Pin17=PB11
Mcu.Pin18=PD12
Mcu.Pin19=PD13
Mcu.Pin2=PC14-OSC32_IN
Mcu.Pin20=PC8
Mcu.Pin21=PC9
Mcu.Pin22=PA9
Mcu.Pin23=PA10
Mcu.Pin24=PA11
Mcu.Pin25=PA12
Mcu.Pin26=PA13
Mcu.Pin27=PA14
Mcu.Pin28=PC10
Mcu.Pin29=PC11
Mcu.Pin3=PC15-OSC32_OUT
Mcu.Pin30=PC12
Mcu.Pin31=PD2
Mcu.Pin32=PD3
Mcu.Pin33=PD4
Mcu.Pin34=PB4
Mcu.Pin35=PB5
Mcu.Pin36=PB6
Mcu.Pin37=PB7
Mcu.Pin38=PE0
Mcu.Pin39=PE1
Mcu.Pin4=PH0-OSC_IN
Mcu.Pin40=VP_SYS_VS_Systick
Mcu.Pin41=VP_TIM1_VS_ClockSourceINT
Mcu.Pin42=VP_TIM2_VS_ClockSourceINT
Mcu.Pin5=PH1-OSC_OUT
Mcu.Pin6=PC0
Mcu.Pin7=PC1
Mcu.Pin8=PC2
Mcu.Pin9=PC3
Mcu.PinsNb=43
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_2
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.UART5_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:1\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PA11.GPIO_Label=LED1
PA11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PA11.GPIO_PuPd=GPIO_NOPULL
PA11.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA11.Locked=true
PA11.PinState=GPIO_PIN_SET
PA11.Signal=GPIO_Output
PA12.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PA12.GPIO_Label=LED2
PA12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PA12.GPIO_PuPd=GPIO_NOPULL
PA12.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA12.Locked=true
PA12.PinState=GPIO_PIN_SET
PA12.Signal=GPIO_Output
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.GPIOParameters=GPIO_Label
PB10.GPIO_Label=GRAY_SCL
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=GRAY_SDA
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB4.Locked=true
PB4.Signal=S_TIM3_CH1
PB5.Locked=true
PB5.Signal=S_TIM3_CH2
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC0.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC0.GPIO_Label=LED1_R
PC0.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC0.GPIO_PuPd=GPIO_NOPULL
PC0.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC0.Locked=true
PC0.PinState=GPIO_PIN_SET
PC0.Signal=GPIO_Output
PC1.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC1.GPIO_Label=LED1_G
PC1.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC1.GPIO_PuPd=GPIO_NOPULL
PC1.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC1.Locked=true
PC1.PinState=GPIO_PIN_SET
PC1.Signal=GPIO_Output
PC10.Locked=true
PC10.Signal=SDIO_D2
PC11.Locked=true
PC11.Signal=SDIO_D3
PC12.Locked=true
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC2.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC2.GPIO_Label=LED1_B
PC2.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC2.GPIO_PuPd=GPIO_NOPULL
PC2.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC2.Locked=true
PC2.PinState=GPIO_PIN_SET
PC2.Signal=GPIO_Output
PC3.GPIOParameters=GPIO_PuPd,GPIO_Label
PC3.GPIO_Label=USER
PC3.GPIO_PuPd=GPIO_PULLUP
PC3.Locked=true
PC3.Signal=GPIO_Input
PC4.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC4.GPIO_Label=GRAY_SOFT_SCL
PC4.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC4.GPIO_PuPd=GPIO_PULLUP
PC4.Locked=true
PC4.PinState=GPIO_PIN_RESET
PC4.Signal=GPIO_Output
PC5.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC5.GPIO_Label=GRAY_SOFT_SDA
PC5.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC5.GPIO_PuPd=GPIO_PULLUP
PC5.Locked=true
PC5.Signal=GPIO_Output
PC8.Locked=true
PC8.Signal=SDIO_D0
PC9.Locked=true
PC9.Signal=SDIO_D1
PD12.Locked=true
PD12.Signal=S_TIM4_CH1
PD13.Locked=true
PD13.Signal=S_TIM4_CH2
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD3.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PD3.GPIO_Label=BNO_INT
PD3.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PD3.GPIO_PuPd=GPIO_PULLUP
PD3.Locked=true
PD3.Signal=GPXTI3
PD4.GPIOParameters=GPIO_Label
PD4.GPIO_Label=BNO_RST
PD4.Locked=true
PD4.Signal=GPIO_Output
PE0.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE0.GPIO_Label=KEY1
PE0.GPIO_Mode=GPIO_MODE_INPUT
PE0.GPIO_PuPd=GPIO_PULLUP
PE0.Locked=true
PE0.Signal=GPIO_Input
PE1.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE1.GPIO_Label=KEY2
PE1.GPIO_Mode=GPIO_MODE_INPUT
PE1.GPIO_PuPd=GPIO_PULLUP
PE1.Locked=true
PE1.Signal=GPIO_Input
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE13.Locked=true
PE13.Signal=S_TIM1_CH3
PE14.Locked=true
PE14.Signal=S_TIM1_CH4
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE2.GPIO_Label=KEY3
PE2.GPIO_Mode=GPIO_MODE_INPUT
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE3.GPIO_Label=KEY4
PE3.GPIO_Mode=GPIO_MODE_INPUT
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE9.Locked=true
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x1000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=ctrl_c_project.ioc
ProjectManager.ProjectName=ctrl_c_project
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x2000
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_I2C1_Init-I2C1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_TIM4_Init-TIM4-false-HAL-true,8-MX_I2C2_Init-I2C2-false-HAL-true,9-MX_TIM1_Init-TIM1-false-HAL-true,10-MX_UART5_Init-UART5-false-HAL-true,11-MX_TIM2_Init-TIM2-false-HAL-true
RCC.48MHZClocksFreq_Value=48000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQ=7
RCC.PLLQCLKFreq_Value=48000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.GPXTI3.0=GPIO_EXTI3
SH.GPXTI3.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM1_CH4.0=TIM1_CH4,PWM Generation4 CH4
SH.S_TIM1_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,Encoder_Interface
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,Encoder_Interface
SH.S_TIM4_CH2.ConfNb=1
TIM1.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM1.IPParameters=Channel-PWM Generation2 CH2,Channel-PWM Generation4 CH4,Period,Prescaler,Channel-PWM Generation1 CH1,Channel-PWM Generation3 CH3
TIM1.Period=1000-1
TIM1.Prescaler=7-1
TIM2.IPParameters=Prescaler,Period
TIM2.Period=1000-1
TIM2.Prescaler=84-1
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IC1Filter=1
TIM3.IC2Filter=1
TIM3.IPParameters=EncoderMode,IC1Filter,IC2Filter
TIM4.EncoderMode=TIM_ENCODERMODE_TI12
TIM4.IC1Filter=1
TIM4.IC2Filter=1
TIM4.IPParameters=EncoderMode,IC1Filter,IC2Filter
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.BaudRate=115200
USART1.IPParameters=VirtualMode,BaudRate
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=custom
